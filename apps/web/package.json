{"name": "my-pomodoro-web", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write .", "prepare": "husky"}, "dependencies": {"react": "18.2.0", "react-dom": "18.2.0", "styled-components": "6.0.7"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "husky": "^9.1.7", "lint-staged": "^16.1.5", "prettier": "^3.0.1", "typescript": "5.2.2", "vite": "^4.4.5"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{js,jsx,json,md}": ["prettier --write"]}}