# Testing Strategy

Defining a comprehensive testing approach for the My Pomodoro fullstack application.

## Testing Pyramid

```
E2E Tests
    /        \
   /          \
Integration Tests
   \          /
    \        /
Unit Tests (Frontend & Backend)
```

## Test Organization

### Frontend Tests

```
apps/web/tests/
├── unit/
│   ├── components/
│   │   ├── Timer.test.tsx
│   │   ├── SessionTracker.test.tsx
│   │   └── Settings.test.tsx
│   ├── hooks/
│   │   ├── useTimer.test.ts
│   │   ├── useSessions.test.ts
│   │   └── usePreferences.test.ts
│   └── utils/
│       ├── timeUtils.test.ts
│       └── audioUtils.test.ts
├── integration/
│   ├── TimerFlow.test.ts
│   ├── SessionTracking.test.ts
│   └── Preferences.test.ts
└── setupTests.ts
```

### Backend Tests

```
apps/api/tests/
├── unit/
│   ├── services/
│   │   ├── sessionService.test.ts
│   │   └── preferenceService.test.ts
│   └── utils/
│       ├── validation.test.ts
│       └── db.test.ts
├── integration/
│   ├── sessionsApi.test.ts
│   └── preferencesApi.test.ts
└── setupTests.ts
```

### E2E Tests

```
tests/e2e/
├── timerFlow.test.ts
├── sessionTracking.test.ts
├── preferences.test.ts
└── pomodoroCycle.test.ts
```

## Test Examples

### Frontend Component Test

```typescript
// apps/web/tests/unit/components/Timer.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Timer } from '../../../src/components/Timer/Timer';

describe('Timer Component', () => {
  const mockProps = {
    workDuration: 1500,
    breakDuration: 300,
    onSessionComplete: jest.fn()
  };

 beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with initial time display', () => {
    render(<Timer {...mockProps} />);
    expect(screen.getByText('25:00')).toBeInTheDocument();
    expect(screen.getByText('Work Session')).toBeInTheDocument();
  });

  it('starts timer when Start button is clicked', () => {
    jest.useFakeTimers();
    render(<Timer {...mockProps} />);
    
    const startButton = screen.getByText('Start');
    fireEvent.click(startButton);
    
    // Advance timers by 1 second
    jest.advanceTimersByTime(1000);
    
    // Timer should have updated
    expect(screen.getByText('24:59')).toBeInTheDocument();
    
    jest.useRealTimers();
  });

  it('switches to break session after work session completes', () => {
    jest.useFakeTimers();
    render(<Timer {...mockProps} />);
    
    // Set work duration to 1 second for testing
    const shortWorkProps = {
      ...mockProps,
      workDuration: 1
    };
    
    render(<Timer {...shortWorkProps} />);
    
    const startButton = screen.getByText('Start');
    fireEvent.click(startButton);
    
    // Advance timers by 1 second
    jest.advanceTimersByTime(1000);
    
    // Should switch to break session
    expect(screen.getByText('Break Time')).toBeInTheDocument();
    expect(screen.getByText('05:00')).toBeInTheDocument();
    
    jest.useRealTimers();
  });
  
 it('pauses and resumes timer correctly', () => {
    jest.useFakeTimers();
    render(<Timer {...mockProps} />);
    
    // Start timer
    const startButton = screen.getByText('Start');
    fireEvent.click(startButton);
    
    // Advance timers by 1 second
    jest.advanceTimersByTime(1000);
    expect(screen.getByText('24:59')).toBeInTheDocument();
    
    // Pause timer
    const pauseButton = screen.getByText('Pause');
    fireEvent.click(pauseButton);
    
    // Advance timers by another second (should not affect display)
    jest.advanceTimersByTime(1000);
    expect(screen.getByText('24:59')).toBeInTheDocument();
    
    // Resume timer
    fireEvent.click(startButton);
    jest.advanceTimersByTime(1000);
    expect(screen.getByText('24:58')).toBeInTheDocument();
    
    jest.useRealTimers();
  });
});
```

### Backend API Test

```typescript
// apps/api/tests/integration/sessionsApi.test.ts
import { createServer, Server } from 'http';
import { apiHandler } from '../../src/routes/sessions/create';
import { createMockRequest, createMockResponse } from '../utils/mockHttp';

describe('Sessions API', () => {
  it('creates a new session with valid data', async () => {
    const mockSession = {
      userId: 'user123',
      startTime: new Date().toISOString(),
      duration: 1500,
      type: 'work',
      completed: false
    };

    const req = createMockRequest('POST', mockSession);
    const res = createMockResponse();

    await apiHandler(req, res);

    expect(res.statusCode).toBe(201);
    expect(res._getData()).toMatchObject({
      id: expect.any(String),
      ...mockSession
    });
  });

  it('returns 40 for invalid session data', async () => {
    const invalidSession = {
      userId: 'user123',
      // Missing required fields
    };

    const req = createMockRequest('POST', invalidSession);
    const res = createMockResponse();

    await apiHandler(req, res);

    expect(res.statusCode).toBe(400);
    expect(res._getData()).toHaveProperty('error');
  });
  
  it('handles database errors gracefully', async () => {
    // Mock database error
    jest.mock('../../src/utils/db', () => ({
      saveSession: jest.fn().mockRejectedValue(new Error('Database connection failed'))
    }));
    
    const mockSession = {
      userId: 'user123',
      startTime: new Date().toISOString(),
      duration: 1500,
      type: 'work',
      completed: false
    };

    const req = createMockRequest('POST', mockSession);
    const res = createMockResponse();

    await apiHandler(req, res);

    expect(res.statusCode).toBe(500);
    expect(res._getData()).toHaveProperty('error');
  });
});
```

### E2E Test

```typescript
// tests/e2e/pomodoroCycle.test.ts
import { test, expect } from '@playwright/test';

test('complete pomodoro cycle', async ({ page }) => {
  // Navigate to the app
  await page.goto('http://localhost:3000');

  // Verify initial state
  await expect(page.getByText('Work Session')).toBeVisible();
  await expect(page.getByText('25:00')).toBeVisible();

  // Start the timer
  await page.getByText('Start').click();

  // Wait for work session to complete (fast-forward in test)
  await page.waitForTimeout(1000); // In real test, we'd wait for timer to reach 00:00

  // Verify transition to break
  await expect(page.getByText('Break Time')).toBeVisible();
  await expect(page.getByText('05:00')).toBeVisible();

 // Start break timer
  await page.getByText('Start').click();

  // Wait for break to complete
  await page.waitForTimeout(1000); // In real test, we'd wait for timer to reach 00:00

  // Verify return to work session
  await expect(page.getByText('Work Session')).toBeVisible();
  await expect(page.getByText('25:00')).toBeVisible();
});

test('customize timer settings', async ({ page }) => {
  // Navigate to the app
  await page.goto('http://localhost:3000');
  
  // Navigate to settings
  await page.click('text=Settings');
  
  // Change work duration to 30 minutes
  await page.fill('[name="workDuration"]', '1800');
  
  // Save settings
  await page.click('text=Save');
  
  // Return to timer
  await page.click('text=Timer');
  
  // Verify new work duration
  await expect(page.getByText('30:00')).toBeVisible();
});
```

### Accessibility Test

```typescript
// apps/web/tests/accessibility/Timer.a11y.test.tsx
import { render } from '@testing-library/react';
import { axe } from 'jest-axe';
import { Timer } from '../../../src/components/Timer/Timer';

describe('Timer Component Accessibility', () => {
  it('should have no accessibility violations', async () => {
    const { container } = render(<Timer workDuration={1500} breakDuration={300} onSessionComplete={jest.fn()} />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should have proper aria labels for buttons', () => {
    const { getByText } = render(<Timer workDuration={1500} breakDuration={300} onSessionComplete={jest.fn()} />);
    
    const startButton = getByText('Start');
    expect(startButton).toHaveAttribute('aria-label', 'Start timer');
    
    const resetButton = getByText('Reset');
    expect(resetButton).toHaveAttribute('aria-label', 'Reset timer');
  });
});
```

### Performance Test

```typescript
// apps/web/tests/performance/Timer.perf.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Timer } from '../../../src/components/Timer/Timer';

describe('Timer Component Performance', () => {
  it('should render quickly', () => {
    const startTime = performance.now();
    render(<Timer workDuration={1500} breakDuration={300} onSessionComplete={jest.fn()} />);
    const endTime = performance.now();
    
    expect(endTime - startTime).toBeLessThan(50); // Should render in less than 50ms
  });

  it('should update timer display efficiently', () => {
    jest.useFakeTimers();
    render(<Timer workDuration={1500} breakDuration={300} onSessionComplete={jest.fn()} />);
    
    const startButton = screen.getByText('Start');
    fireEvent.click(startButton);
    
    // Measure time for 10 timer updates
    const startTime = performance.now();
    for (let i = 0; i < 100; i++) {
      jest.advanceTimersByTime(1000);
    }
    const endTime = performance.now();
    
    // Should update 100 times in less than 100ms
    expect(endTime - startTime).toBeLessThan(100);
    
    jest.useRealTimers();
  });
});