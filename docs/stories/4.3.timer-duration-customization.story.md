# Story 4.3: Timer Duration Customization

## Status
Draft

## Story
**As a** user,
**I want** to customize the duration of work and break sessions,
**so that** I can adapt the Pomodoro technique to my personal preferences.

## Acceptance Criteria
1. Users can set custom durations for work sessions (default 25 minutes)
2. Users can set custom durations for break sessions (default 5 minutes)
3. Custom durations are validated to ensure they are reasonable
4. Changes to timer durations take effect immediately
5. Default durations can be easily restored
6. Custom duration settings persist between application sessions

## Tasks / Subtasks
- [ ] Implement work duration customization (AC: 1, 3, 4, 6)
  - [ ] Add work duration input field in settings UI
  - [ ] Implement validation for work duration (minimum 1 minute, maximum 120 minutes)
  - [ ] Connect work duration setting to usePreferences hook
  - [ ] Ensure changes take effect immediately in timer component
- [ ] Implement break duration customization (AC: 2, 3, 4, 6)
  - [ ] Add break duration input field in settings UI
  - [ ] Implement validation for break duration (minimum 30 seconds, maximum 60 minutes)
  - [ ] Connect break duration setting to usePreferences hook
  - [ ] Ensure changes take effect immediately in timer component
- [ ] Implement default duration restoration (AC: 5)
  - [ ] Add "Restore Defaults" button in settings UI
  - [ ] Implement functionality to reset durations to default values
  - [ ] Confirm with user before restoring defaults
- [ ] Ensure settings persistence (AC: 6)
  - [ ] Verify work and break duration settings persist after app restart
  - [ ] Test settings persistence across different browser sessions
- [ ] Create unit tests for duration customization features (AC: all)
  - [ ] Test work duration input validation
  - [ ] Test break duration input validation
  - [ ] Test default duration restoration
  - [ ] Test settings persistence
- [ ] Create integration tests for timer duration settings flow (AC: all)
  - [ ] Test complete duration settings workflow
  - [ ] Test timer functionality with custom durations
  - [ ] Test immediate effect of duration changes
  - [ ] Test cross-browser compatibility

## Dev Notes
### Previous Story Insights
This story builds upon the settings screen implementation in Story 4.1 and audio alert customization in Story 4.2, which established the foundation for user preferences management. The timer duration customization will integrate with the existing settings UI and use the usePreferences hook that was previously implemented. The responsive design patterns and PWA capabilities established in previous stories provide the foundation for a consistent user experience when customizing timer durations across different devices.

Source: docs/stories/4.1.settings-screen-implementation.story.md

### Data Models
The implementation will use the existing UserPreferences data model defined in the architecture:

**UserPreferences Model:**
- userId: string - Unique identifier for the user (could be device ID for local storage)
- workDuration: number - Work session duration in seconds (default: 1500)
- breakDuration: number - Break session duration in seconds (default: 300)
- audioEnabled: boolean - Whether audio alerts are enabled
- audioVolume: number - Audio volume level (0-100)
- theme: string - UI theme preference (optional)

The workDuration and breakDuration fields will be used for timer duration customization.

Source: architecture/data-models.md

### API Specifications
No new API endpoints need to be implemented for timer duration customization. The implementation will use the existing client-side IndexedDB storage layer to retrieve and save user preferences. The dataService.ts file from Story 3.1 provides the necessary methods for retrieving and saving preferences.

Source: architecture/api-specification.md

### Component Specifications
The timer duration customization will integrate with existing components:

- Settings.tsx: Will be enhanced with duration customization UI elements
- Timer.tsx: Will receive updated duration values from usePreferences hook
- common/Input.tsx: Will be used for duration input fields
- common/Button.tsx: Will be used for restore defaults button

The duration customization options should be organized in a clear section within the settings screen.

Source: architecture/frontend-architecture.md#L20-L23

### File Locations
Following the unified project structure, timer duration customization implementation should be done in:

- apps/web/src/pages/Settings/Settings.tsx (enhance with duration customization UI)
- apps/web/src/pages/Settings/Settings.styles.ts (styling for duration customization UI)
- apps/web/src/components/Timer/Timer.tsx (ensure proper integration with updated preferences)
- apps/web/src/hooks/usePreferences.ts (ensure proper integration with duration settings)
- apps/web/src/utils/timeUtils.ts (add utility functions for duration validation and formatting if needed)

Test files should be created in:
- apps/web/tests/unit/pages/Settings.test.tsx
- apps/web/tests/unit/hooks/usePreferences.test.ts
- apps/web/tests/unit/utils/timeUtils.test.ts
- apps/web/tests/integration/timerDurationFlow.test.ts

Source: architecture/unified-project-structure.md

### Testing Requirements
Unit tests should be created for the duration customization components:

- Test work duration input validation with various values
- Test break duration input validation with various values
- Test default duration restoration functionality
- Test settings persistence across app sessions
- Test error handling for invalid duration inputs

Integration tests should validate the complete duration settings workflow:

- Test complete duration settings workflow from UI to timer component
- Test timer functionality with custom durations
- Test immediate effect of duration changes
- Test cross-browser compatibility for duration settings
- Test accessibility compliance for duration customization components

Testing frameworks to use:
- Jest for unit testing [Source: architecture/tech-stack.md#L20]
- React Testing Library for component integration tests [Source: architecture/tech-stack.md#L20]
- Cypress for end-to-end testing of duration settings and timer functionality [Source: architecture/tech-stack.md#L22]

Source: architecture/testing-strategy.md

### Technical Constraints
- Use TypeScript with explicit interfaces for data models [Source: architecture/coding-standards.md#L12]
- Follow naming conventions (camelCase for functions, PascalCase for interfaces) [Source: architecture/coding-standards.md#L18-L26]
- Never mutate state directly - use proper state management patterns [Source: architecture/coding-standards.md#L11]
- All duration operations should handle errors gracefully
- Use the existing data access layer pattern shown in the frontend architecture
- Follow the established project structure for file organization
- Ensure the duration customization UI is accessible and works well on all supported devices
- Duration values should be validated to ensure they are reasonable (not too short or too long)
- Duration changes should take effect immediately without requiring a page refresh

### Project Structure Notes
The project structure is already established from previous stories with the correct monorepo conventions. The timer duration customization implementation will enhance the existing Settings and Timer components following the established patterns in the pages and components directories. The implementation will not require changes to the overall project structure, but will enhance the existing preferences management functionality with timer duration customization capabilities.

Source: docs/stories/4.1.settings-screen-implementation.story.md

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-08-31 | 1.0 | Initial story creation | Scrum Master (Bob) |

## Dev Agent Record

### Agent Model Used

### Debug Log References

### Completion Notes List

### File List

## QA Results

## Story Draft Checklist Validation

### 1. GOAL & CONTEXT CLARITY
- [x] Story goal/purpose is clearly stated
- [x] Relationship to epic goals is evident
- [x] How the story fits into overall system flow is explained
- [x] Dependencies on previous stories are identified (if applicable)
- [x] Business context and value are clear

### 2. TECHNICAL IMPLEMENTATION GUIDANCE
- [x] Key files to create/modify are identified (not necessarily exhaustive)
- [x] Technologies specifically needed for this story are mentioned
- [x] Critical APIs or interfaces are sufficiently described
- [x] Necessary data models or structures are referenced
- [x] Required environment variables are listed (if applicable)
- [x] Any exceptions to standard coding patterns are noted

### 3. REFERENCE EFFECTIVENESS
- [x] References to external documents point to specific relevant sections
- [x] Critical information from previous stories is summarized (not just referenced)
- [x] Context is provided for why references are relevant
- [x] References use consistent format (e.g., `docs/filename.md#section`)

### 4. SELF-CONTAINMENT ASSESSMENT
- [x] Core information needed is included (not overly reliant on external docs)
- [x] Implicit assumptions are made explicit
- [x] Domain-specific terms or concepts are explained
- [x] Edge cases or error scenarios are addressed

### 5. TESTING GUIDANCE
- [x] Required testing approach is outlined
- [x] Key test scenarios are identified
- [x] Success criteria are defined
- [x] Special testing considerations are noted (if applicable)

### VALIDATION RESULT

| Category                             | Status   | Issues |
| ------------------------------------ | -------- | ------ |
| 1. Goal & Context Clarity            | PASS     | None   |
| 2. Technical Implementation Guidance | PASS     | None   |
| 3. Reference Effectiveness           | PASS     | None   |
| 4. Self-Containment Assessment       | PASS     | None   |
| 5. Testing Guidance                  | PASS     | None   |

**Final Assessment: READY**

The story provides sufficient context for implementation. The requirements are clear, technical guidance is provided, references are effective, and testing approaches are outlined. A developer agent should be able to implement this story successfully without requiring additional clarification.